a:58:{s:13:"68792ba73305d";a:13:{s:3:"tag";s:13:"68792ba73305d";s:3:"url";s:42:"http://vacanct/employer/worker/detail?id=1";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752771495.130273;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8032640;s:14:"processingTime";d:0.4696180820465088;}s:13:"68792bde30c2f";a:13:{s:3:"tag";s:13:"68792bde30c2f";s:3:"url";s:36:"http://vacanct/employer/favorite/add";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752771550.135014;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7649256;s:14:"processingTime";d:0.2423999309539795;}s:13:"68792c92e887d";a:13:{s:3:"tag";s:13:"68792c92e887d";s:3:"url";s:36:"http://vacanct/employer/favorite/add";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752771730.857894;s:10:"statusCode";i:500;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7624984;s:14:"processingTime";d:0.5163171291351318;}s:13:"68792cba1ca00";a:13:{s:3:"tag";s:13:"68792cba1ca00";s:3:"url";s:36:"http://vacanct/employer/favorite/add";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752771769.997089;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8022176;s:14:"processingTime";d:0.4576551914215088;}s:13:"68792d8be207b";a:13:{s:3:"tag";s:13:"68792d8be207b";s:3:"url";s:53:"http://vacanct/employer/favorite/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752771979.79459;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6452192;s:14:"processingTime";d:0.1546628475189209;}s:13:"68792dd643656";a:13:{s:3:"tag";s:13:"68792dd643656";s:3:"url";s:53:"http://vacanct/employer/favorite/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772053.885559;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8052400;s:14:"processingTime";d:1.7667500972747803;}s:13:"68792e023422f";a:13:{s:3:"tag";s:13:"68792e023422f";s:3:"url";s:39:"http://vacanct/employer/favorite/remove";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772098.154186;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7877400;s:14:"processingTime";d:0.24477887153625488;}s:13:"68792e0947e5b";a:13:{s:3:"tag";s:13:"68792e0947e5b";s:3:"url";s:53:"http://vacanct/employer/favorite/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772105.230678;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7906992;s:14:"processingTime";d:0.27851200103759766;}s:13:"68792e2112195";a:13:{s:3:"tag";s:13:"68792e2112195";s:3:"url";s:43:"http://vacanct/employer/favorite/statistics";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772129.033417;s:10:"statusCode";i:200;s:8:"sqlCount";i:5;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7702176;s:14:"processingTime";d:0.2261190414428711;}s:13:"68792f689d760";a:13:{s:3:"tag";s:13:"68792f689d760";s:3:"url";s:36:"http://vacanct/employer/profile/view";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772456.54771;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7857688;s:14:"processingTime";d:0.30069994926452637;}s:13:"68792f895af4f";a:13:{s:3:"tag";s:13:"68792f895af4f";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772489.333702;s:10:"statusCode";i:500;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7638728;s:14:"processingTime";d:0.19836187362670898;}s:13:"68792fab73f50";a:13:{s:3:"tag";s:13:"68792fab73f50";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772523.380885;s:10:"statusCode";i:200;s:8:"sqlCount";i:12;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7937200;s:14:"processingTime";d:0.2802579402923584;}s:13:"68792fb840276";a:13:{s:3:"tag";s:13:"68792fb840276";s:3:"url";s:36:"http://vacanct/employer/profile/view";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772536.204323;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7857688;s:14:"processingTime";d:0.2967069149017334;}s:13:"6879302b5f606";a:13:{s:3:"tag";s:13:"6879302b5f606";s:3:"url";s:41:"http://vacanct/employer/profile/languages";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772651.310399;s:10:"statusCode";i:200;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6245008;s:14:"processingTime";d:0.10087990760803223;}s:13:"6879304012e21";a:13:{s:3:"tag";s:13:"6879304012e21";s:3:"url";s:47:"http://vacanct/employer/profile/change-language";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772672.041957;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7908504;s:14:"processingTime";d:0.25917983055114746;}s:13:"687b22472a98c";a:13:{s:3:"tag";s:13:"687b22472a98c";s:3:"url";s:36:"http://vacanct/employer/profile/view";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752900162.97466;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7769328;s:14:"processingTime";d:8.4008629322052;}s:13:"687b2251c91e7";a:13:{s:3:"tag";s:13:"687b2251c91e7";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752900177.771826;s:10:"statusCode";i:200;s:8:"sqlCount";i:12;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7849480;s:14:"processingTime";d:0.42853307723999023;}s:13:"687b24d498eab";a:13:{s:3:"tag";s:13:"687b24d498eab";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752900820.476854;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7782208;s:14:"processingTime";d:0.5292129516601562;}s:13:"687b268291395";a:13:{s:3:"tag";s:13:"687b268291395";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901250.509717;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7790120;s:14:"processingTime";d:0.2682368755340576;}s:13:"687b271f7dfdc";a:13:{s:3:"tag";s:13:"687b271f7dfdc";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901407.411327;s:10:"statusCode";i:422;s:8:"sqlCount";i:5;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7561120;s:14:"processingTime";d:0.2441251277923584;}s:13:"687b2729c589e";a:13:{s:3:"tag";s:13:"687b2729c589e";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901417.746121;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7541712;s:14:"processingTime";d:0.2461869716644287;}s:13:"687b273278681";a:13:{s:3:"tag";s:13:"687b273278681";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901426.437402;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7911240;s:14:"processingTime";d:0.3019428253173828;}s:13:"687b287feea04";a:13:{s:3:"tag";s:13:"687b287feea04";s:3:"url";s:40:"http://vacanct/employer/auth/verify-code";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901759.875247;s:10:"statusCode";i:200;s:8:"sqlCount";i:11;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7965128;s:14:"processingTime";d:0.5553619861602783;}s:13:"687b288857227";a:13:{s:3:"tag";s:13:"687b288857227";s:3:"url";s:43:"http://vacanct/vacancy/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901768.319362;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6192568;s:14:"processingTime";d:1.4184300899505615;}s:13:"687b289395461";a:13:{s:3:"tag";s:13:"687b289395461";s:3:"url";s:52:"http://vacanct/employer/vacancy/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901779.571475;s:10:"statusCode";i:200;s:8:"sqlCount";i:5;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7739440;s:14:"processingTime";d:0.19701290130615234;}s:13:"687b29bdd0b3a";a:13:{s:3:"tag";s:13:"687b29bdd0b3a";s:3:"url";s:38:"http://vacanct/employer/vacancy/create";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902077.777932;s:10:"statusCode";i:500;s:8:"sqlCount";i:6;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7808928;s:14:"processingTime";d:0.28189992904663086;}s:13:"687b2c04b7382";a:13:{s:3:"tag";s:13:"687b2c04b7382";s:3:"url";s:38:"http://vacanct/employer/vacancy/create";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902660.651064;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8026648;s:14:"processingTime";d:0.38112592697143555;}s:13:"687b2c36832e0";a:13:{s:3:"tag";s:13:"687b2c36832e0";s:3:"url";s:41:"http://vacanct/employer/vacancy/view?id=1";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902710.467138;s:10:"statusCode";i:404;s:8:"sqlCount";i:4;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7737480;s:14:"processingTime";d:0.2495889663696289;}s:13:"687b2c3fd4b82";a:13:{s:3:"tag";s:13:"687b2c3fd4b82";s:3:"url";s:52:"http://vacanct/employer/vacancy/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902719.82433;s:10:"statusCode";i:500;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7865120;s:14:"processingTime";d:0.2430119514465332;}s:13:"687b2c63ddada";a:13:{s:3:"tag";s:13:"687b2c63ddada";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902755.840391;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705184;s:14:"processingTime";d:0.2123880386352539;}s:13:"687b2c6ccabbc";a:13:{s:3:"tag";s:13:"687b2c6ccabbc";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902764.77869;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705200;s:14:"processingTime";d:0.19720911979675293;}s:13:"687b2c75c6d8c";a:13:{s:3:"tag";s:13:"687b2c75c6d8c";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902773.778685;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705184;s:14:"processingTime";d:0.1886138916015625;}s:13:"687b2e115736f";a:13:{s:3:"tag";s:13:"687b2e115736f";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903185.269219;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705624;s:14:"processingTime";d:0.23155498504638672;}s:13:"687b2e160ad57";a:13:{s:3:"tag";s:13:"687b2e160ad57";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903190.002496;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705624;s:14:"processingTime";d:0.2190999984741211;}s:13:"687b2e1a2484b";a:13:{s:3:"tag";s:13:"687b2e1a2484b";s:3:"url";s:41:"http://vacanct/employer/vacancy/view?id=1";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903194.093778;s:10:"statusCode";i:404;s:8:"sqlCount";i:4;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7737920;s:14:"processingTime";d:0.23766183853149414;}s:13:"687b2e1e8b68f";a:13:{s:3:"tag";s:13:"687b2e1e8b68f";s:3:"url";s:52:"http://vacanct/employer/vacancy/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903198.508743;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7848280;s:14:"processingTime";d:0.27022695541381836;}s:13:"687b2e380af15";a:13:{s:3:"tag";s:13:"687b2e380af15";s:3:"url";s:42:"http://vacanct/employer/vacancy/view?id=11";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903224.000227;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7844280;s:14:"processingTime";d:0.20969414710998535;}s:13:"687b2e5579a2f";a:13:{s:3:"tag";s:13:"687b2e5579a2f";s:3:"url";s:40:"http://vacanct/employer/vacancy/statuses";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903253.461421;s:10:"statusCode";i:200;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:5795168;s:14:"processingTime";d:0.0731961727142334;}s:13:"687b2e9511815";a:13:{s:3:"tag";s:13:"687b2e9511815";s:3:"url";s:38:"http://vacanct/employer/vacancy/create";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903316.938204;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8026976;s:14:"processingTime";d:0.3429698944091797;}s:13:"687b2e9c3a4f6";a:13:{s:3:"tag";s:13:"687b2e9c3a4f6";s:3:"url";s:52:"http://vacanct/employer/vacancy/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903324.181272;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7856392;s:14:"processingTime";d:0.25042295455932617;}s:13:"687b2eb390a5a";a:13:{s:3:"tag";s:13:"687b2eb390a5a";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903347.561202;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705624;s:14:"processingTime";d:0.17289495468139648;}s:13:"687b2f53a10af";a:13:{s:3:"tag";s:13:"687b2f53a10af";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903507.561299;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8017096;s:14:"processingTime";d:0.3327291011810303;}s:13:"687b304c5d320";a:13:{s:3:"tag";s:13:"687b304c5d320";s:3:"url";s:38:"http://vacanct/employer/vacancy/delete";s:4:"ajax";i:0;s:6:"method";s:6:"DELETE";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903756.30223;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7922600;s:14:"processingTime";d:0.25559306144714355;}s:13:"6880bc53afbfe";a:13:{s:3:"tag";s:13:"6880bc53afbfe";s:3:"url";s:21:"php yii migrate/fresh";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1753267281.552945;s:10:"statusCode";i:0;s:8:"sqlCount";i:179;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6363128;s:14:"processingTime";d:7.141268968582153;}s:13:"6880bcdcec19f";a:13:{s:3:"tag";s:13:"6880bcdcec19f";s:3:"url";s:15:"http://vacanct/";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753267420.210163;s:10:"statusCode";i:200;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6412560;s:14:"processingTime";d:2.289947032928467;}s:13:"6880bcdfde32e";a:13:{s:3:"tag";s:13:"6880bcdfde32e";s:3:"url";s:38:"http://vacanct/ws/info?t=1753267423865";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753267423.867469;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6385792;s:14:"processingTime";d:0.3190338611602783;}s:13:"6880bd22906cc";a:13:{s:3:"tag";s:13:"6880bd22906cc";s:3:"url";s:30:"http://vacanct//auth/send-code";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753267490.422051;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6293328;s:14:"processingTime";d:0.20256304740905762;}s:13:"6880c37034aa6";a:13:{s:3:"tag";s:13:"6880c37034aa6";s:3:"url";s:39:"http://vacanct//employer/auth/send-code";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753269104.062689;s:10:"statusCode";i:500;s:8:"sqlCount";i:4;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8013656;s:14:"processingTime";d:2.200978994369507;}s:13:"6880c68a3a1ad";a:13:{s:3:"tag";s:13:"6880c68a3a1ad";s:3:"url";s:39:"http://vacanct//employer/auth/send-code";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753269898.119455;s:10:"statusCode";i:500;s:8:"sqlCount";i:4;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8030656;s:14:"processingTime";d:0.3506758213043213;}s:13:"6880cce0b995d";a:13:{s:3:"tag";s:13:"6880cce0b995d";s:3:"url";s:8:"php yii ";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1753271520.727587;s:10:"statusCode";i:0;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:4269864;s:14:"processingTime";d:1.1317400932312012;}s:13:"6880cd01d8c6b";a:13:{s:3:"tag";s:13:"6880cd01d8c6b";s:3:"url";s:8:"php yii ";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1753271553.824468;s:10:"statusCode";i:0;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:4270736;s:14:"processingTime";d:1.1649830341339111;}s:13:"6880cd26c62fd";a:13:{s:3:"tag";s:13:"6880cd26c62fd";s:3:"url";s:8:"php yii ";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1753271590.742623;s:10:"statusCode";i:0;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:4270296;s:14:"processingTime";d:1.4158601760864258;}s:13:"6880cd44aaf09";a:13:{s:3:"tag";s:13:"6880cd44aaf09";s:3:"url";s:8:"php yii ";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1753271620.668177;s:10:"statusCode";i:0;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:4377328;s:14:"processingTime";d:8.288213014602661;}s:13:"6880cdaf03426";a:13:{s:3:"tag";s:13:"6880cdaf03426";s:3:"url";s:39:"http://vacanct//employer/auth/send-code";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753271726.915258;s:10:"statusCode";i:500;s:8:"sqlCount";i:4;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8055832;s:14:"processingTime";d:0.28340911865234375;}s:13:"6880ce9d9526b";a:13:{s:3:"tag";s:13:"6880ce9d9526b";s:3:"url";s:8:"php yii ";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1753271965.56749;s:10:"statusCode";i:0;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:4273296;s:14:"processingTime";d:1.1912469863891602;}s:13:"6880cebdd1ead";a:13:{s:3:"tag";s:13:"6880cebdd1ead";s:3:"url";s:8:"php yii ";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1753271997.814446;s:10:"statusCode";i:0;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:4273872;s:14:"processingTime";d:1.221580982208252;}s:13:"6880cfc17e157";a:13:{s:3:"tag";s:13:"6880cfc17e157";s:3:"url";s:8:"php yii ";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1753272257.480941;s:10:"statusCode";i:0;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:4278152;s:14:"processingTime";d:1.153892993927002;}s:13:"6880cff6c0fb4";a:13:{s:3:"tag";s:13:"6880cff6c0fb4";s:3:"url";s:8:"php yii ";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1753272310.703647;s:10:"statusCode";i:0;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:4278120;s:14:"processingTime";d:1.1937239170074463;}}