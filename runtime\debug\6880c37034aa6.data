a:14:{s:6:"config";s:1743:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:6:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:54:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:64:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:10035:"a:1:{s:8:"messages";a:19:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753269104.156495;i:4;a:0:{}i:5;i:2611632;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753269104.164041;i:4;a:0:{}i:5;i:2789992;}i:2;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753269104.164082;i:4;a:0:{}i:5;i:2790792;}i:3;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1753269104.196263;i:4;a:0:{}i:5;i:3733400;}i:4;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753269104.213955;i:4;a:0:{}i:5;i:4181128;}i:5;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753269104.232388;i:4;a:0:{}i:5;i:4686704;}i:6;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753269104.233897;i:4;a:0:{}i:5;i:4711536;}i:90;a:6:{i:0;s:43:"Route requested: '/employer/auth/send-code'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1753269104.240317;i:4;a:0:{}i:5;i:5014616;}i:91;a:6:{i:0;s:24:"Loading module: employer";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753269104.240337;i:4;a:0:{}i:5;i:5016256;}i:92;a:6:{i:0;s:37:"Route to run: employer/auth/send-code";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1753269104.418794;i:4;a:0:{}i:5;i:5344232;}i:93;a:6:{i:0;s:39:"Rate limit skipped: user not logged in.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1753269104.738794;i:4;a:0:{}i:5;i:5556888;}i:94;a:6:{i:0;s:81:"Running action: app\modules\employer\controllers\AuthController::actionSendCode()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1753269104.738912;i:4;a:0:{}i:5;i:5558160;}i:95;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1753269105.600795;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7285696;}i:98;a:6:{i:0;s:84:"SELECT * FROM "employers" WHERE ("phone"='+998930960195') AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.655149;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7288216;}i:101;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.736404;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7328072;}i:104;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.768227;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7375960;}i:107;a:6:{i:0;s:82:"SELECT EXISTS(SELECT * FROM "employers" WHERE "employers"."phone"='+998930960195')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753269106.144141;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7745296;}i:110;a:6:{i:0;s:43:"Model not inserted due to validation error.";i:1;i:4;i:2;s:27:"yii\db\ActiveRecord::insert";i:3;d:1753269106.146018;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7737680;}i:111;a:6:{i:0;s:148:"Failed to create employer: {"name":["\u0418\u043c\u044f \u0440\u0430\u0431\u043e\u0442\u043e\u0434\u0430\u0442\u0435\u043b\u044f cannot be blank."]}";i:1;i:1;i:2;s:66:"app\modules\employer\services\EmployerService::getOrCreateEmployer";i:3;d:1753269106.146071;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:32;s:8:"function";s:5:"error";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7738624;}}}";s:9:"profiling";s:13559:"a:3:{s:6:"memory";i:8013656;s:4:"time";d:2.200978994369507;s:8:"messages";a:10:{i:96;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1753269105.600828;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7287200;}i:97;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1753269105.655113;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7289496;}i:99;a:6:{i:0;s:84:"SELECT * FROM "employers" WHERE ("phone"='+998930960195') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.65519;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7291320;}i:100;a:6:{i:0;s:84:"SELECT * FROM "employers" WHERE ("phone"='+998930960195') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.662911;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7293696;}i:102;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.736476;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7329936;}i:103;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.766527;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7352112;}i:105;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.76826;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7377824;}i:106;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.771502;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7379760;}i:108;a:6:{i:0;s:82:"SELECT EXISTS(SELECT * FROM "employers" WHERE "employers"."phone"='+998930960195')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269106.144191;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7748312;}i:109;a:6:{i:0;s:82:"SELECT EXISTS(SELECT * FROM "employers" WHERE "employers"."phone"='+998930960195')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269106.145774;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7749920;}}}";s:2:"db";s:12266:"a:1:{s:8:"messages";a:8:{i:99;a:6:{i:0;s:84:"SELECT * FROM "employers" WHERE ("phone"='+998930960195') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.65519;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7291320;}i:100;a:6:{i:0;s:84:"SELECT * FROM "employers" WHERE ("phone"='+998930960195') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.662911;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7293696;}i:102;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.736476;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7329936;}i:103;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.766527;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7352112;}i:105;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.76826;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7377824;}i:106;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269105.771502;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7379760;}i:108;a:6:{i:0;s:82:"SELECT EXISTS(SELECT * FROM "employers" WHERE "employers"."phone"='+998930960195')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269106.144191;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7748312;}i:109;a:6:{i:0;s:82:"SELECT EXISTS(SELECT * FROM "employers" WHERE "employers"."phone"='+998930960195')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269106.145774;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7749920;}}}";s:5:"event";s:3264:"a:18:{i:0;a:5:{s:4:"time";d:1753269104.238518;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1753269104.419591;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1753269104.419619;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:3;a:5:{s:4:"time";d:1753269104.738884;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"app\modules\employer\controllers\AuthController";}i:4;a:5:{s:4:"time";d:1753269105.297982;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:5;a:5:{s:4:"time";d:1753269105.6551;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:6;a:5:{s:4:"time";d:1753269105.735596;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\employer\models\Employer";}i:7;a:5:{s:4:"time";d:1753269105.7983;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\employer\models\Employer";}i:8;a:5:{s:4:"time";d:1753269106.027473;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:9;a:5:{s:4:"time";d:1753269106.027843;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:10;a:5:{s:4:"time";d:1753269106.146007;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\employer\models\Employer";}i:11;a:5:{s:4:"time";d:1753269106.1469;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"app\modules\employer\controllers\AuthController";}i:12;a:5:{s:4:"time";d:1753269106.181475;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:13;a:5:{s:4:"time";d:1753269106.181494;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:14;a:5:{s:4:"time";d:1753269106.181508;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:15;a:5:{s:4:"time";d:1753269106.181519;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:16;a:5:{s:4:"time";d:1753269106.304935;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:17;a:5:{s:4:"time";d:1753269106.305058;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1753269104.114996;s:3:"end";d:1753269106.316193;s:6:"memory";i:8013656;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:16624:"a:3:{s:8:"messages";a:83:{i:7;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239434;i:4;a:0:{}i:5;i:4941656;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239465;i:4;a:0:{}i:5;i:4942408;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239479;i:4;a:0:{}i:5;i:4943480;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.23949;i:4;a:0:{}i:5;i:4944232;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239501;i:4;a:0:{}i:5;i:4944984;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:16:"telegram/webhook";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239512;i:4;a:0:{}i:5;i:4945736;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:34:"telegram/registration/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239522;i:4;a:0:{}i:5;i:4946488;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:32:"telegram/profession/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239531;i:4;a:0:{}i:5;i:4947240;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST worker/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239557;i:4;a:0:{}i:5;i:4948048;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:22:"POST worker/auth/login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239572;i:4;a:0:{}i:5;i:4948848;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:23:"POST worker/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239583;i:4;a:0:{}i:5;i:4950288;}i:18;a:6:{i:0;a:3:{s:4:"rule";s:22:"GET worker/auth/verify";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239593;i:4;a:0:{}i:5;i:4951088;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:24:"POST worker/auth/refresh";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239604;i:4;a:0:{}i:5;i:4951896;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:23:"GET worker/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239612;i:4;a:0:{}i:5;i:4952696;}i:21;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET worker/vacancy/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239621;i:4;a:0:{}i:5;i:4953504;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET worker/vacancy/detail/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239631;i:4;a:0:{}i:5;i:4954320;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET worker/profile/index";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239639;i:4;a:0:{}i:5;i:4955128;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:25:"PUT worker/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239647;i:4;a:0:{}i:5;i:4955936;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:32:"POST worker/profile/upload-audio";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239658;i:4;a:0:{}i:5;i:4956752;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239669;i:4;a:0:{}i:5;i:4957560;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/auth/verify-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239681;i:4;a:0:{}i:5;i:4958368;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:40:"POST employer/auth/complete-registration";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239692;i:4;a:0:{}i:5;i:4959200;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/auth/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239701;i:4;a:0:{}i:5;i:4960008;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:25:"POST employer/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239714;i:4;a:0:{}i:5;i:4960816;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/worker/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239723;i:4;a:0:{}i:5;i:4961624;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239731;i:4;a:0:{}i:5;i:4962432;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/detail";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.23974;i:4;a:0:{}i:5;i:4964520;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/worker/professions";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239749;i:4;a:0:{}i:5;i:4965328;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:33:"GET employer/worker/by-profession";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239759;i:4;a:0:{}i:5;i:4966144;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/worker/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239768;i:4;a:0:{}i:5;i:4966952;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/worker/check-access";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239776;i:4;a:0:{}i:5;i:4967768;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/worker/unlock-contact";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239785;i:4;a:0:{}i:5;i:4968584;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST employer/favorite/add";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239796;i:4;a:0:{}i:5;i:4969392;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/favorite/remove";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239807;i:4;a:0:{}i:5;i:4970200;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/favorite/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239815;i:4;a:0:{}i:5;i:4971008;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/favorite/toggle";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239827;i:4;a:0:{}i:5;i:4971816;}i:43;a:6:{i:0;a:3:{s:4:"rule";s:31:"POST employer/favorite/bulk-add";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239839;i:4;a:0:{}i:5;i:4972624;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:34:"POST employer/favorite/bulk-remove";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.23985;i:4;a:0:{}i:5;i:4973440;}i:45;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/favorite/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239879;i:4;a:0:{}i:5;i:4974256;}i:46;a:6:{i:0;a:3:{s:4:"rule";s:35:"GET employer/favorite/by-profession";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239888;i:4;a:0:{}i:5;i:4975072;}i:47;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/profile/view";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239897;i:4;a:0:{}i:5;i:4975880;}i:48;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT employer/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239905;i:4;a:0:{}i:5;i:4976688;}i:49;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239916;i:4;a:0:{}i:5;i:4977496;}i:50;a:6:{i:0;a:3:{s:4:"rule";s:37:"POST employer/profile/change-language";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239927;i:4;a:0:{}i:5;i:4978312;}i:51;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/profile/languages";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239935;i:4;a:0:{}i:5;i:4979120;}i:52;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/profile/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239947;i:4;a:0:{}i:5;i:4979928;}i:53;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239957;i:4;a:0:{}i:5;i:4980736;}i:54;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/vacancy/view";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239965;i:4;a:0:{}i:5;i:4981544;}i:55;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/create";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239976;i:4;a:0:{}i:5;i:4982352;}i:56;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT employer/vacancy/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239985;i:4;a:0:{}i:5;i:4983160;}i:57;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.239996;i:4;a:0:{}i:5;i:4983968;}i:58;a:6:{i:0;a:3:{s:4:"rule";s:30:"DELETE employer/vacancy/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240005;i:4;a:0:{}i:5;i:4984776;}i:59;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240016;i:4;a:0:{}i:5;i:4985584;}i:60;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/vacancy/statuses";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240025;i:4;a:0:{}i:5;i:4986392;}i:61;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/access/filter-page";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240034;i:4;a:0:{}i:5;i:4987200;}i:62;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/access/calculate-price";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240045;i:4;a:0:{}i:5;i:4988016;}i:63;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/access/purchase";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240055;i:4;a:0:{}i:5;i:4988824;}i:64;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/access/my-purchases";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240063;i:4;a:0:{}i:5;i:4989640;}i:65;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/access/available";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240073;i:4;a:0:{}i:5;i:4996080;}i:66;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/access/unlock-contact";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240081;i:4;a:0:{}i:5;i:4996896;}i:67;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/access/contact-status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.24009;i:4;a:0:{}i:5;i:4997712;}i:68;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/access/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240098;i:4;a:0:{}i:5;i:4998520;}i:69;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/tariff/plans";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240107;i:4;a:0:{}i:5;i:4999328;}i:70;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/tariff/detail";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240116;i:4;a:0:{}i:5;i:5000136;}i:71;a:6:{i:0;a:3:{s:4:"rule";s:27:"GET employer/tariff/compare";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240124;i:4;a:0:{}i:5;i:5000944;}i:72;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/tariff/filtered";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240133;i:4;a:0:{}i:5;i:5001752;}i:73;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/tariff/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240142;i:4;a:0:{}i:5;i:5002560;}i:74;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/tariff/recommended";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.24015;i:4;a:0:{}i:5;i:5003368;}i:75;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/tariff/discounts";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240158;i:4;a:0:{}i:5;i:5004176;}i:76;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/payment/methods";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240168;i:4;a:0:{}i:5;i:5004984;}i:77;a:6:{i:0;a:3:{s:4:"rule";s:27:"GET employer/payment/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240177;i:4;a:0:{}i:5;i:5005792;}i:78;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/payment/history";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240185;i:4;a:0:{}i:5;i:5006600;}i:79;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/payment/callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240196;i:4;a:0:{}i:5;i:5007408;}i:80;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/payment/click-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240207;i:4;a:0:{}i:5;i:5008224;}i:81;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/payment/payme-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240219;i:4;a:0:{}i:5;i:5009040;}i:82;a:6:{i:0;a:3:{s:4:"rule";s:37:"POST employer/payment/uzcard-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240229;i:4;a:0:{}i:5;i:5009856;}i:83;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/payment/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240239;i:4;a:0:{}i:5;i:5010664;}i:84;a:6:{i:0;a:3:{s:4:"rule";s:27:"POST employer/payment/retry";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.24025;i:4;a:0:{}i:5;i:5011472;}i:85;a:6:{i:0;a:3:{s:4:"rule";s:21:"employer/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240261;i:4;a:0:{}i:5;i:5012224;}i:86;a:6:{i:0;a:3:{s:4:"rule";s:29:"employer/vacancy/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240269;i:4;a:0:{}i:5;i:5012976;}i:87;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240278;i:4;a:0:{}i:5;i:5013728;}i:88;a:6:{i:0;a:3:{s:4:"rule";s:12:"<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.24029;i:4;a:0:{}i:5;i:5014480;}i:89;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269104.240299;i:4;a:0:{}i:5;i:5014856;}}s:5:"route";s:23:"employer/auth/send-code";s:6:"action";s:65:"app\modules\employer\controllers\AuthController::actionSendCode()";}";s:7:"request";s:3508:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:500;s:14:"requestHeaders";a:10:{s:12:"content-type";s:16:"application/json";s:10:"user-agent";s:21:"PostmanRuntime/7.44.1";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"0cac238c-b0e6-4787-a67f-51c4a5a22959";s:4:"host";s:7:"vacanct";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:2:"32";s:6:"cookie";s:42:"PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k";}s:15:"responseHeaders";a:8:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:32:"Access-Control-Allow-Credentials";s:5:"false";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6880c37034aa6";s:16:"X-Debug-Duration";s:5:"2,191";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6880c37034aa6";}s:5:"route";s:23:"employer/auth/send-code";s:6:"action";s:65:"app\modules\employer\controllers\AuthController::actionSendCode()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:32:"{
    "phone": "+998930960195"
}";s:7:"Decoded";a:0:{}}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:12:"CONTENT_TYPE";s:16:"application/json";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.1";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"0cac238c-b0e6-4787-a67f-51c4a5a22959";s:9:"HTTP_HOST";s:7:"vacanct";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:2:"32";s:11:"HTTP_COOKIE";s:42:"PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:7:"vacanct";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:40:"D:/OSPanel/domains/ish_top/web/index.php";s:11:"REMOTE_PORT";s:5:"58695";s:12:"REDIRECT_URL";s:24:"/employer/auth/send-code";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:25:"//employer/auth/send-code";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1753269104.062689;s:12:"REQUEST_TIME";i:1753269104;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:1:{s:9:"PHPSESSID";s:32:"gnoa088o0hon1vgqigs0tb9cu9vd163k";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"6880c37034aa6";s:3:"url";s:39:"http://vacanct//employer/auth/send-code";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753269104.062689;s:10:"statusCode";i:500;s:8:"sqlCount";i:4;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8013656;s:14:"processingTime";d:2.200978994369507;}s:10:"exceptions";a:0:{}}