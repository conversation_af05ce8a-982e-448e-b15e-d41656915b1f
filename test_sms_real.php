<?php

// Тестовый скрипт для реальной отправки SMS
require_once __DIR__ . '/vendor/autoload.php';

defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

require __DIR__ . '/vendor/yiisoft/yii2/Yii.php';

$config = require __DIR__ . '/config/web.php';
(new yii\web\Application($config));

use app\common\services\SmsService;

echo "=== Тест реальной отправки SMS ===\n";

try {
    $smsService = new SmsService();
    
    $testPhone = '+998930960195';
    $testCode = '1234';
    $userType = 'employer';
    
    echo "Отправка SMS на номер: {$testPhone}\n";
    echo "Код: {$testCode}\n";
    echo "Тип пользователя: {$userType}\n";
    echo "Время: " . date('Y-m-d H:i:s') . "\n\n";
    
    // Показываем какой текст будет отправлен
    $userTypeText = $userType === 'worker' ? 'Ishchi' : 'Ish beruvchi';
    $approvedTemplate = "Tasdiqlash kodi [user_type] hisobiga kirish uchun IshTop platformasida: Kod: [code]";
    $expectedMessage = str_replace(['[user_type]', '[code]'], [$userTypeText, $testCode], $approvedTemplate);
    
    echo "Ожидаемый текст SMS:\n";
    echo "'{$expectedMessage}'\n\n";
    
    $result = $smsService->sendValidationCode($testPhone, $testCode, $userType);
    
    echo "Результат отправки:\n";
    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    if ($result['success']) {
        echo "✅ SMS отправлен успешно!\n";
        if (isset($result['message_id'])) {
            echo "ID сообщения: {$result['message_id']}\n";
        }
        if (isset($result['response_data'])) {
            echo "Данные ответа: " . json_encode($result['response_data'], JSON_UNESCAPED_UNICODE) . "\n";
        }
    } else {
        echo "❌ Ошибка отправки SMS:\n";
        echo "Ошибка: " . ($result['error'] ?? 'Неизвестная ошибка') . "\n";
        if (isset($result['details'])) {
            echo "Детали: {$result['details']}\n";
        }
        if (isset($result['status_code'])) {
            echo "HTTP код: {$result['status_code']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Исключение: " . $e->getMessage() . "\n";
    echo "Файл: " . $e->getFile() . "\n";
    echo "Строка: " . $e->getLine() . "\n";
}

echo "\n=== Тест завершен ===\n";
