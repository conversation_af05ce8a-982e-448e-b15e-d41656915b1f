a:10:{s:6:"config";s:1745:"a:5:{s:10:"phpVersion";s:6:"8.1.31";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"8.1.31";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:6:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:54:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:64:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:6158:"a:1:{s:8:"messages";a:20:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753271520.750611;i:4;a:0:{}i:5;i:2339616;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753271520.751999;i:4;a:0:{}i:5;i:2449480;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753271520.752009;i:4;a:0:{}i:5;i:2449904;}i:3;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753271520.752421;i:4;a:0:{}i:5;i:2476256;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753271520.75243;i:4;a:0:{}i:5;i:2477432;}i:5;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753271520.759607;i:4;a:0:{}i:5;i:2987648;}i:6;a:6:{i:0;s:190:"POST https://notify.eskiz.uz/api/auth/login
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

email=nasritdinov651%40gmail.com&password=z5EGITeBUdHpWTffcit7NRc9Xx6ytz6FOaCz21fp";i:1;i:4;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1753271520.779433;i:4;a:0:{}i:5;i:3573880;}i:9;a:6:{i:0;s:33:"Eskiz.uz authorization successful";i:1;i:4;i:2;s:41:"app\common\services\SmsService::authorize";i:3;d:1753271521.703279;i:4;a:0:{}i:5;i:4099384;}i:10;a:6:{i:0;s:63:"Starting SMS validation code send to +998930960195 for employer";i:1;i:4;i:2;s:50:"app\common\services\SmsService::sendValidationCode";i:3;d:1753271521.703727;i:4;a:0:{}i:5;i:4094760;}i:11;a:6:{i:0;s:148:"Using approved template with full text: Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234 Kodni hech kimga bermang!";i:1;i:4;i:2;s:50:"app\common\services\SmsService::sendValidationCode";i:3;d:1753271521.703753;i:4;a:0:{}i:5;i:4095488;}i:12;a:6:{i:0;s:161:"Sending SMS with approved template to +998930960195: Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234 Kodni hech kimga bermang!";i:1;i:4;i:2;s:50:"app\common\services\SmsService::sendValidationCode";i:3;d:1753271521.703755;i:4;a:0:{}i:5;i:4096056;}i:13;a:6:{i:0;s:162:"Attempting to send SMS to +998930960195 with message: Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234 Kodni hech kimga bermang!";i:1;i:4;i:2;s:39:"app\common\services\SmsService::sendSms";i:3;d:1753271521.703757;i:4;a:0:{}i:5;i:4096624;}i:14;a:6:{i:0;s:48:"Got authorization token for SMS to +998930960195";i:1;i:4;i:2;s:39:"app\common\services\SmsService::sendSms";i:3;d:1753271521.720631;i:4;a:0:{}i:5;i:4097192;}i:15;a:6:{i:0;s:47:"Normalized phone: +998930960195 -> 998930960195";i:1;i:4;i:2;s:39:"app\common\services\SmsService::sendSms";i:3;d:1753271521.720913;i:4;a:0:{}i:5;i:4098040;}i:16;a:6:{i:0;s:213:"Sending SMS request to eskiz.uz API with data: {"mobile_phone":"998930960195","message":"Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234 Kodni hech kimga bermang!","from":"4546"}";i:1;i:4;i:2;s:39:"app\common\services\SmsService::sendSms";i:3;d:1753271521.720931;i:4;a:0:{}i:5;i:4099048;}i:17;a:6:{i:0;s:558:"POST https://notify.eskiz.uz/api/message/sms/send
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTU4NjM1MjMsImlhdCI6MTc1MzI3MTUyMywicm9sZSI6InVzZXIiLCJzaWduIjoiNzA0ZjNkMDBlNmMwMjljMjcxMjYxOGJmMWM2Y2I3ZGQ5ODNjMGNmYzczZGRjOGU1OGMyMjZiZTcxNTQyMDA4OCIsInN1YiI6IjQ5MzgifQ.TkzvNrqYHt7dEPd_G331-1y-DQ_5McxASdAu0sNJFtI
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

mobile_phone=998930960195&message=Tasdiqlash+kodi+Ish+beruvchi+hisobiga+kirish+uchun+IshTop+platformasida%3A+Kod%3A+1234+Kodni+hech+kimga+bermang%21&from=4546";i:1;i:4;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1753271521.721099;i:4;a:0:{}i:5;i:4105800;}i:20;a:6:{i:0;s:57:"Eskiz.uz API response status: 400 for phone +998930960195";i:1;i:4;i:2;s:39:"app\common\services\SmsService::sendSms";i:3;d:1753271521.77541;i:4;a:0:{}i:5;i:4107568;}i:21;a:6:{i:0;s:383:"Eskiz.uz API response content: {"id":"1761b6a1-d858-4b1f-96ca-ceb0a583cf53","message":"Этот смс текст еще не прошёл модерацию. Сначала добавьте его через API - Шаблоны - Отправить шаблон или через кабинет my.eskiz.uz - СМС - Мои тексты.","status":"error"} for phone +998930960195";i:1;i:4;i:2;s:39:"app\common\services\SmsService::sendSms";i:3;d:1753271521.775418;i:4;a:0:{}i:5;i:4108392;}i:22;a:6:{i:0;s:365:"SMS sending failed to +998930960195: {"id":"1761b6a1-d858-4b1f-96ca-ceb0a583cf53","message":"Этот смс текст еще не прошёл модерацию. Сначала добавьте его через API - Шаблоны - Отправить шаблон или через кабинет my.eskiz.uz - СМС - Мои тексты.","status":"error"}";i:1;i:1;i:2;s:39:"app\common\services\SmsService::sendSms";i:3;d:1753271521.775437;i:4;a:0:{}i:5;i:4109216;}i:23;a:6:{i:0;s:894:"SMS send result for +998930960195: {"success":false,"error":"Failed to send SMS","details":"{\"id\":\"1761b6a1-d858-4b1f-96ca-ceb0a583cf53\",\"message\":\"\u042d\u0442\u043e\u0442 \u0441\u043c\u0441 \u0442\u0435\u043a\u0441\u0442 \u0435\u0449\u0435 \u043d\u0435 \u043f\u0440\u043e\u0448\u0451\u043b \u043c\u043e\u0434\u0435\u0440\u0430\u0446\u0438\u044e. \u0421\u043d\u0430\u0447\u0430\u043b\u0430 \u0434\u043e\u0431\u0430\u0432\u044c\u0442\u0435 \u0435\u0433\u043e \u0447\u0435\u0440\u0435\u0437 API - \u0428\u0430\u0431\u043b\u043e\u043d\u044b - \u041e\u0442\u043f\u0440\u0430\u0432\u0438\u0442\u044c \u0448\u0430\u0431\u043b\u043e\u043d \u0438\u043b\u0438 \u0447\u0435\u0440\u0435\u0437 \u043a\u0430\u0431\u0438\u043d\u0435\u0442 my.eskiz.uz - \u0421\u041c\u0421 - \u041c\u043e\u0438 \u0442\u0435\u043a\u0441\u0442\u044b.\",\"status\":\"error\"}","phone":"+998930960195","status_code":"400"}";i:1;i:4;i:2;s:50:"app\common\services\SmsService::sendValidationCode";i:3;d:1753271521.775492;i:4;a:0:{}i:5;i:4106856;}}}";s:9:"profiling";s:2090:"a:3:{s:6:"memory";i:4269864;s:4:"time";d:1.1317400932312012;s:8:"messages";a:4:{i:7;a:6:{i:0;s:190:"POST https://notify.eskiz.uz/api/auth/login
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

email=nasritdinov651%40gmail.com&password=z5EGITeBUdHpWTffcit7NRc9Xx6ytz6FOaCz21fp";i:1;i:80;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1753271520.779437;i:4;a:0:{}i:5;i:3574256;}i:8;a:6:{i:0;s:190:"POST https://notify.eskiz.uz/api/auth/login
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

email=nasritdinov651%40gmail.com&password=z5EGITeBUdHpWTffcit7NRc9Xx6ytz6FOaCz21fp";i:1;i:96;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1753271521.695927;i:4;a:0:{}i:5;i:3577176;}i:18;a:6:{i:0;s:558:"POST https://notify.eskiz.uz/api/message/sms/send
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTU4NjM1MjMsImlhdCI6MTc1MzI3MTUyMywicm9sZSI6InVzZXIiLCJzaWduIjoiNzA0ZjNkMDBlNmMwMjljMjcxMjYxOGJmMWM2Y2I3ZGQ5ODNjMGNmYzczZGRjOGU1OGMyMjZiZTcxNTQyMDA4OCIsInN1YiI6IjQ5MzgifQ.TkzvNrqYHt7dEPd_G331-1y-DQ_5McxASdAu0sNJFtI
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

mobile_phone=998930960195&message=Tasdiqlash+kodi+Ish+beruvchi+hisobiga+kirish+uchun+IshTop+platformasida%3A+Kod%3A+1234+Kodni+hech+kimga+bermang%21&from=4546";i:1;i:80;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1753271521.7211;i:4;a:0:{}i:5;i:4106176;}i:19;a:6:{i:0;s:558:"POST https://notify.eskiz.uz/api/message/sms/send
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTU4NjM1MjMsImlhdCI6MTc1MzI3MTUyMywicm9sZSI6InVzZXIiLCJzaWduIjoiNzA0ZjNkMDBlNmMwMjljMjcxMjYxOGJmMWM2Y2I3ZGQ5ODNjMGNmYzczZGRjOGU1OGMyMjZiZTcxNTQyMDA4OCIsInN1YiI6IjQ5MzgifQ.TkzvNrqYHt7dEPd_G331-1y-DQ_5McxASdAu0sNJFtI
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

mobile_phone=998930960195&message=Tasdiqlash+kodi+Ish+beruvchi+hisobiga+kirish+uchun+IshTop+platformasida%3A+Kod%3A+1234+Kodni+hech+kimga+bermang%21&from=4546";i:1;i:96;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1753271521.775227;i:4;a:0:{}i:5;i:4109112;}}}";s:2:"db";s:27:"a:1:{s:8:"messages";a:0:{}}";s:5:"event";s:1506:"a:8:{i:0;a:5:{s:4:"time";d:1753271520.776663;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:1;a:5:{s:4:"time";d:1753271520.776709;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:2;a:5:{s:4:"time";d:1753271521.696997;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:3;a:5:{s:4:"time";d:1753271521.697012;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:4;a:5:{s:4:"time";d:1753271521.721012;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:5;a:5:{s:4:"time";d:1753271521.721028;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:6;a:5:{s:4:"time";d:1753271521.775324;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:7;a:5:{s:4:"time";d:1753271521.775347;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:90:"a:3:{s:5:"start";d:1753271520.741351;s:3:"end";d:1753271521.87312;s:6:"memory";i:4269864;}";s:4:"dump";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"6880cce0b995d";s:3:"url";s:8:"php yii ";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1753271520.727587;s:10:"statusCode";i:0;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:4269864;s:14:"processingTime";d:1.1317400932312012;}s:10:"exceptions";a:0:{}}