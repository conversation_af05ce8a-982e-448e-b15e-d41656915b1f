<?php

namespace app\common\services;

use Yii;
use yii\httpclient\Client;
use yii\base\Component;
use yii\base\Exception;

/**
 * Сервис для отправки SMS через eskiz.uz API
 * 
 * Обеспечивает авторизацию и отправку SMS уведомлений
 */
class SmsService extends Component
{
    /**
     * @var string URL API eskiz.uz
     */
    private $apiUrl = 'https://notify.eskiz.uz/api';
    
    /**
     * @var string Email для авторизации
     */
    private $email;

    /**
     * @var string Пароль для авторизации
     */
    private $password;
    
    /**
     * @var string Токен авторизации
     */
    private $token;
    
    /**
     * @var Client HTTP клиент
     */
    private $httpClient;
    
    /**
     * @var int Время жизни токена в секундах (по умолчанию 1 час)
     */
    private $tokenLifetime = 3600;

    /**
     * Инициализация сервиса
     */
    public function init()
    {
        parent::init();

        $this->email = Yii::$app->params['eskizSmsServiceLogin'] ?? null;
        $this->password = Yii::$app->params['eskizSmsServicePassword'] ?? null;
        $this->httpClient = new Client();
    }

    /**
     * Авторизация в API eskiz.uz
     *
     * @return bool
     * @throws Exception
     */
    public function authorize()
    {
        try {
            $response = $this->httpClient->createRequest()
                ->setMethod('POST')
                ->setUrl($this->apiUrl . '/auth/login')
                ->setData([
                    'email' => $this->email,
                    'password' => $this->password
                ])
                ->send();

            if ($response->isOk) {
                $data = $response->data;
                if (isset($data['data']['token'])) {
                    $this->token = $data['data']['token'];
                    
                    // Сохраняем токен в кеше
                    Yii::$app->cache->set('eskiz_token', $this->token, $this->tokenLifetime);
                    
                    Yii::info('Eskiz.uz authorization successful', __METHOD__);
                    return true;
                }
            }
            
            Yii::error('Eskiz.uz authorization failed: ' . $response->content, __METHOD__);
            return false;
            
        } catch (\Exception $e) {
            Yii::error('Eskiz.uz authorization error: ' . $e->getMessage(), __METHOD__);
            throw new Exception('Failed to authorize with eskiz.uz: ' . $e->getMessage());
        }
    }

    /**
     * Получить действующий токен авторизации
     * 
     * @return string|null
     * @throws Exception
     */
    private function getToken()
    {
        // Проверяем конфигурацию
        if (empty($this->email) || empty($this->password)) {
            throw new Exception('Eskiz.uz credentials not configured');
        }
        
        // Проверяем кеш
        $cachedToken = Yii::$app->cache->get('eskiz_token');
        if ($cachedToken) {
            $this->token = $cachedToken;
            return $this->token;
        }
        
        // Если токена нет в кеше, авторизуемся заново
        if ($this->authorize()) {
            return $this->token;
        }
        
        return null;
    }

    /**
     * Отправить SMS
     *
     * @param string $phone Номер телефона в формате +998XXXXXXXXX
     * @param string $message Текст сообщения
     * @param string $from Отправитель (по умолчанию '4546')
     * @return array Результат отправки
     * @throws Exception
     */
    public function sendSms($phone, $message, $from = '4546')
    {
        Yii::info("Attempting to send SMS to {$phone} with message: {$message}", __METHOD__);

        $token = $this->getToken();
        if (!$token) {
            Yii::error("Failed to get authorization token for SMS to {$phone}", __METHOD__);
            throw new Exception('Failed to get authorization token');
        }

        Yii::info("Got authorization token for SMS to {$phone}", __METHOD__);

        try {
            $normalizedPhone = $this->normalizePhone($phone);
            Yii::info("Normalized phone: {$phone} -> {$normalizedPhone}", __METHOD__);

            $requestData = [
                'mobile_phone' => $normalizedPhone,
                'message' => $message,
                'from' => $from
            ];

            Yii::info("Sending SMS request to eskiz.uz API with data: " . json_encode($requestData), __METHOD__);

            $response = $this->httpClient->createRequest()
                ->setMethod('POST')
                ->setUrl($this->apiUrl . '/message/sms/send')
                ->setHeaders([
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type' => 'application/json'
                ])
                ->setData($requestData)
                ->send();

            Yii::info("Eskiz.uz API response status: {$response->statusCode} for phone {$phone}", __METHOD__);
            Yii::info("Eskiz.uz API response content: {$response->content} for phone {$phone}", __METHOD__);

            if ($response->isOk) {
                $data = $response->data;

                Yii::info('SMS sent successfully to ' . $phone . ' with response: ' . json_encode($data), __METHOD__);

                return [
                    'success' => true,
                    'message_id' => $data['id'] ?? null,
                    'status' => $data['status'] ?? 'sent',
                    'phone' => $phone,
                    'message' => $message,
                    'response_data' => $data
                ];
            } else {
                // Если токен истек, пробуем авторизоваться заново
                if ($response->statusCode == 401) {
                    Yii::warning("Token expired for SMS to {$phone}, trying to re-authorize", __METHOD__);
                    Yii::$app->cache->delete('eskiz_token');
                    $this->token = null;

                    // Повторная попытка с новым токеном
                    $newToken = $this->getToken();
                    if ($newToken) {
                        Yii::info("Got new token, retrying SMS to {$phone}", __METHOD__);
                        return $this->sendSms($phone, $message, $from);
                    }
                }

                Yii::error("SMS sending failed to {$phone}: {$response->content}", __METHOD__);

                return [
                    'success' => false,
                    'error' => 'Failed to send SMS',
                    'details' => $response->content,
                    'phone' => $phone,
                    'status_code' => $response->statusCode
                ];
            }

        } catch (\Exception $e) {
            Yii::error("SMS sending error to {$phone}: {$e->getMessage()}", __METHOD__);

            return [
                'success' => false,
                'error' => 'SMS sending error: ' . $e->getMessage(),
                'phone' => $phone
            ];
        }
    }

    /**
     * Отправить SMS с кодом валидации
     *
     * @param string $phone Номер телефона
     * @param string $code Код валидации
     * @param string $userType Тип пользователя (worker, employer)
     * @return array Результат отправки
     */
    public function sendValidationCode($phone, $code, $userType = 'worker')
    {
        Yii::info("Starting SMS validation code send to {$phone} for {$userType}", __METHOD__);

        // Исключения для тестовых номеров (только для разработки)
        $testNumbers = ['+998901111111', '+998902222222'];
        if (in_array($phone, $testNumbers)) {
            Yii::info("Test number detected: {$phone}, simulating SMS send", __METHOD__);
            return [
                'success' => true,
                'message' => 'Test SMS sent (simulation for development)',
                'provider' => 'test',
                'phone' => $phone,
                'code' => $code
            ];
        }

        // Попробуем разные варианты одобренного текста
        $userTypeText = $userType === 'worker' ? 'Ishchi' : 'Ish beruvchi';

        // Вариант 1: Точно как в одобренном шаблоне (с переменными в квадратных скобках)
        $message = "Tasdiqlash kodi [user_type] hisobiga kirish uchun IshTop platformasida: Kod: [code] Kodni hech kimga bermang!";

        Yii::info("Trying exact approved template text: {$message}", __METHOD__);

        Yii::info("Sending SMS with approved template to {$phone}: {$message}", __METHOD__);

        $result = $this->sendSms($phone, $message);

        Yii::info("SMS send result for {$phone}: " . json_encode($result), __METHOD__);

        return $result;
    }

    /**
     * Генерировать случайный код валидации
     *
     * @param int $length Длина кода (по умолчанию 4)
     * @return string
     */
    public function generateValidationCode($length = 4)
    {
        return str_pad(rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }

    /**
     * Сохранить код валидации в кеше
     *
     * @param string $phone Номер телефона
     * @param string $code Код валидации
     * @param int $ttl Время жизни в секундах (по умолчанию 5 минут)
     * @return bool
     */
    public function saveValidationCode($phone, $code, $ttl = 300)
    {
        $key = 'validation_code_' . md5($phone);
        return Yii::$app->cache->set($key, $code, $ttl);
    }

    /**
     * Проверить код валидации
     *
     * @param string $phone Номер телефона
     * @param string $code Введенный код
     * @return bool
     */
    public function verifyValidationCode($phone, $code)
    {
        $key = 'validation_code_' . md5($phone);
        $savedCode = Yii::$app->cache->get($key);

        if ($savedCode && $savedCode === $code) {
            // Удаляем код после успешной проверки
            Yii::$app->cache->delete($key);
            return true;
        }

        return false;
    }

    /**
     * Нормализация номера телефона для eskiz.uz
     * 
     * @param string $phone
     * @return string
     */
    private function normalizePhone($phone)
    {
        // Удаляем все символы кроме цифр и +
        $phone = preg_replace('/[^\d+]/', '', $phone);
        
        // Если номер начинается с +998, убираем +
        if (strpos($phone, '+998') === 0) {
            $phone = substr($phone, 1);
        }
        
        // Если номер не начинается с 998, добавляем 998
        if (strpos($phone, '998') !== 0) {
            $phone = '998' . $phone;
        }
        
        return $phone;
    }

    /**
     * Получить список шаблонов SMS
     *
     * @return array
     * @throws Exception
     */
    public function getTemplates()
    {
        $token = $this->getToken();
        if (!$token) {
            throw new Exception('Failed to get authorization token');
        }

        try {
            $response = $this->httpClient->createRequest()
                ->setMethod('GET')
                ->setUrl($this->apiUrl . '/user/template')
                ->setHeaders([
                    'Authorization' => 'Bearer ' . $token
                ])
                ->send();

            if ($response->isOk) {
                Yii::info('Templates retrieved successfully', __METHOD__);
                return [
                    'success' => true,
                    'data' => $response->data
                ];
            } else {
                Yii::error('Failed to get templates: ' . $response->content, __METHOD__);
                return [
                    'success' => false,
                    'error' => 'Failed to get templates',
                    'details' => $response->content
                ];
            }

        } catch (\Exception $e) {
            Yii::error('Templates retrieval error: ' . $e->getMessage(), __METHOD__);
            return [
                'success' => false,
                'error' => 'Templates retrieval error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Отправить SMS по шаблону
     *
     * @param string $phone Номер телефона
     * @param int $templateId ID шаблона
     * @param array $variables Переменные для замены в шаблоне
     * @param string $from Отправитель
     * @return array
     * @throws Exception
     */
    public function sendSmsTemplate($phone, $templateId, $variables = [], $from = '4546')
    {
        $token = $this->getToken();
        if (!$token) {
            throw new Exception('Failed to get authorization token');
        }

        try {
            $requestData = [
                'mobile_phone' => $this->normalizePhone($phone),
                'template_id' => $templateId,
                'from' => $from
            ];

            // Добавляем переменные если есть
            if (!empty($variables)) {
                $requestData['variables'] = $variables;
            }

            Yii::info("Sending SMS template {$templateId} to {$phone} with data: " . json_encode($requestData), __METHOD__);

            $response = $this->httpClient->createRequest()
                ->setMethod('POST')
                ->setUrl($this->apiUrl . '/user/template')
                ->setHeaders([
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type' => 'application/json'
                ])
                ->setData($requestData)
                ->send();

            Yii::info("Template SMS API response status: {$response->statusCode} for phone {$phone}", __METHOD__);
            Yii::info("Template SMS API response content: {$response->content} for phone {$phone}", __METHOD__);

            if ($response->isOk) {
                $data = $response->data;

                Yii::info('Template SMS sent successfully to ' . $phone . ' with response: ' . json_encode($data), __METHOD__);

                return [
                    'success' => true,
                    'message_id' => $data['id'] ?? null,
                    'status' => $data['status'] ?? 'sent',
                    'phone' => $phone,
                    'template_id' => $templateId,
                    'response_data' => $data
                ];
            } else {
                Yii::error("Template SMS sending failed to {$phone}: {$response->content}", __METHOD__);

                return [
                    'success' => false,
                    'error' => 'Failed to send template SMS',
                    'details' => $response->content,
                    'phone' => $phone,
                    'status_code' => $response->statusCode
                ];
            }

        } catch (\Exception $e) {
            Yii::error("Template SMS sending error to {$phone}: {$e->getMessage()}", __METHOD__);

            return [
                'success' => false,
                'error' => 'Template SMS sending error: ' . $e->getMessage(),
                'phone' => $phone
            ];
        }
    }

    /**
     * Проверить статус SMS
     * 
     * @param string $messageId ID сообщения
     * @return array Статус сообщения
     */
    public function getSmsStatus($messageId)
    {
        $token = $this->getToken();
        if (!$token) {
            throw new Exception('Failed to get authorization token');
        }

        try {
            $response = $this->httpClient->createRequest()
                ->setMethod('GET')
                ->setUrl($this->apiUrl . '/message/sms/status/' . $messageId)
                ->setHeaders([
                    'Authorization' => 'Bearer ' . $token
                ])
                ->send();

            if ($response->isOk) {
                return [
                    'success' => true,
                    'status' => $response->data
                ];
            }
            
            return [
                'success' => false,
                'error' => 'Failed to get SMS status'
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Error getting SMS status: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Получить баланс аккаунта
     * 
     * @return array Информация о балансе
     */
    public function getBalance()
    {
        $token = $this->getToken();
        if (!$token) {
            throw new Exception('Failed to get authorization token');
        }

        try {
            $response = $this->httpClient->createRequest()
                ->setMethod('GET')
                ->setUrl($this->apiUrl . '/auth/user')
                ->setHeaders([
                    'Authorization' => 'Bearer ' . $token
                ])
                ->send();

            if ($response->isOk) {
                return [
                    'success' => true,
                    'balance' => $response->data
                ];
            }
            
            return [
                'success' => false,
                'error' => 'Failed to get balance'
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Error getting balance: ' . $e->getMessage()
            ];
        }
    }
}
