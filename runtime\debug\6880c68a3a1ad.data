a:14:{s:6:"config";s:1743:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:6:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:54:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:64:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:10034:"a:1:{s:8:"messages";a:19:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753269898.188844;i:4;a:0:{}i:5;i:2611632;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753269898.193014;i:4;a:0:{}i:5;i:2789992;}i:2;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753269898.193046;i:4;a:0:{}i:5;i:2790792;}i:3;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1753269898.221836;i:4;a:0:{}i:5;i:3733400;}i:4;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753269898.236437;i:4;a:0:{}i:5;i:4181128;}i:5;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753269898.251568;i:4;a:0:{}i:5;i:4686704;}i:6;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753269898.253013;i:4;a:0:{}i:5;i:4711536;}i:90;a:6:{i:0;s:43:"Route requested: '/employer/auth/send-code'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1753269898.259624;i:4;a:0:{}i:5;i:5014616;}i:91;a:6:{i:0;s:24:"Loading module: employer";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753269898.259646;i:4;a:0:{}i:5;i:5016256;}i:92;a:6:{i:0;s:37:"Route to run: employer/auth/send-code";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1753269898.27578;i:4;a:0:{}i:5;i:5361232;}i:93;a:6:{i:0;s:39:"Rate limit skipped: user not logged in.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1753269898.284737;i:4;a:0:{}i:5;i:5573888;}i:94;a:6:{i:0;s:81:"Running action: app\modules\employer\controllers\AuthController::actionSendCode()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1753269898.284887;i:4;a:0:{}i:5;i:5575160;}i:95;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1753269898.332271;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7302696;}i:98;a:6:{i:0;s:84:"SELECT * FROM "employers" WHERE ("phone"='+998930960195') AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.407128;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7305216;}i:101;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.422559;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7345072;}i:104;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.480456;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7392960;}i:107;a:6:{i:0;s:82:"SELECT EXISTS(SELECT * FROM "employers" WHERE "employers"."phone"='+998930960195')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.496698;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7762296;}i:110;a:6:{i:0;s:43:"Model not inserted due to validation error.";i:1;i:4;i:2;s:27:"yii\db\ActiveRecord::insert";i:3;d:1753269898.498243;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7754680;}i:111;a:6:{i:0;s:148:"Failed to create employer: {"name":["\u0418\u043c\u044f \u0440\u0430\u0431\u043e\u0442\u043e\u0434\u0430\u0442\u0435\u043b\u044f cannot be blank."]}";i:1;i:1;i:2;s:66:"app\modules\employer\services\EmployerService::getOrCreateEmployer";i:3;d:1753269898.498287;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:32;s:8:"function";s:5:"error";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7755624;}}}";s:9:"profiling";s:13559:"a:3:{s:6:"memory";i:8030656;s:4:"time";d:0.3506758213043213;s:8:"messages";a:10:{i:96;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1753269898.332515;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7304200;}i:97;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1753269898.40706;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7306496;}i:99;a:6:{i:0;s:84:"SELECT * FROM "employers" WHERE ("phone"='+998930960195') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.407189;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7308320;}i:100;a:6:{i:0;s:84:"SELECT * FROM "employers" WHERE ("phone"='+998930960195') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.420368;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7310696;}i:102;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.422614;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7346936;}i:103;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.477923;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7369112;}i:105;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.4805;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7394824;}i:106;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.486806;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7396760;}i:108;a:6:{i:0;s:82:"SELECT EXISTS(SELECT * FROM "employers" WHERE "employers"."phone"='+998930960195')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.496738;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7765312;}i:109;a:6:{i:0;s:82:"SELECT EXISTS(SELECT * FROM "employers" WHERE "employers"."phone"='+998930960195')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.497852;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7766920;}}}";s:2:"db";s:12266:"a:1:{s:8:"messages";a:8:{i:99;a:6:{i:0;s:84:"SELECT * FROM "employers" WHERE ("phone"='+998930960195') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.407189;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7308320;}i:100;a:6:{i:0;s:84:"SELECT * FROM "employers" WHERE ("phone"='+998930960195') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.420368;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7310696;}i:102;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.422614;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7346936;}i:103;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.477923;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7369112;}i:105;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.4805;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7394824;}i:106;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.486806;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:27;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7396760;}i:108;a:6:{i:0;s:82:"SELECT EXISTS(SELECT * FROM "employers" WHERE "employers"."phone"='+998930960195')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.496738;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7765312;}i:109;a:6:{i:0;s:82:"SELECT EXISTS(SELECT * FROM "employers" WHERE "employers"."phone"='+998930960195')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753269898.497852;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\employer\services\EmployerService.php";s:4:"line";i:31;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php";s:4:"line";i:84;s:8:"function";s:19:"getOrCreateEmployer";s:5:"class";s:45:"app\modules\employer\services\EmployerService";s:4:"type";s:2:"->";}}i:5;i:7766920;}}}";s:5:"event";s:3269:"a:18:{i:0;a:5:{s:4:"time";d:1753269898.257364;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1753269898.276396;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1753269898.276428;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:3;a:5:{s:4:"time";d:1753269898.284855;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"app\modules\employer\controllers\AuthController";}i:4;a:5:{s:4:"time";d:1753269898.310309;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:5;a:5:{s:4:"time";d:1753269898.40704;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:6;a:5:{s:4:"time";d:1753269898.422138;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\employer\models\Employer";}i:7;a:5:{s:4:"time";d:1753269898.488075;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\employer\models\Employer";}i:8;a:5:{s:4:"time";d:1753269898.495825;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:9;a:5:{s:4:"time";d:1753269898.495878;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:10;a:5:{s:4:"time";d:1753269898.498228;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\employer\models\Employer";}i:11;a:5:{s:4:"time";d:1753269898.499384;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"app\modules\employer\controllers\AuthController";}i:12;a:5:{s:4:"time";d:1753269898.500214;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:13;a:5:{s:4:"time";d:1753269898.500226;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:14;a:5:{s:4:"time";d:1753269898.500236;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:15;a:5:{s:4:"time";d:1753269898.500245;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:16;a:5:{s:4:"time";d:1753269898.504621;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:17;a:5:{s:4:"time";d:1753269898.504697;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:90:"a:3:{s:5:"start";d:1753269898.15965;s:3:"end";d:1753269898.510499;s:6:"memory";i:8030656;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:16618:"a:3:{s:8:"messages";a:83:{i:7;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258451;i:4;a:0:{}i:5;i:4941656;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258486;i:4;a:0:{}i:5;i:4942408;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258504;i:4;a:0:{}i:5;i:4943480;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258517;i:4;a:0:{}i:5;i:4944232;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258531;i:4;a:0:{}i:5;i:4944984;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:16:"telegram/webhook";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258544;i:4;a:0:{}i:5;i:4945736;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:34:"telegram/registration/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258557;i:4;a:0:{}i:5;i:4946488;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:32:"telegram/profession/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258568;i:4;a:0:{}i:5;i:4947240;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST worker/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258593;i:4;a:0:{}i:5;i:4948048;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:22:"POST worker/auth/login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.25861;i:4;a:0:{}i:5;i:4948848;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:23:"POST worker/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258625;i:4;a:0:{}i:5;i:4950288;}i:18;a:6:{i:0;a:3:{s:4:"rule";s:22:"GET worker/auth/verify";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.25864;i:4;a:0:{}i:5;i:4951088;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:24:"POST worker/auth/refresh";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258656;i:4;a:0:{}i:5;i:4951896;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:23:"GET worker/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.25867;i:4;a:0:{}i:5;i:4952696;}i:21;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET worker/vacancy/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258685;i:4;a:0:{}i:5;i:4953504;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET worker/vacancy/detail/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258698;i:4;a:0:{}i:5;i:4954320;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET worker/profile/index";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.25871;i:4;a:0:{}i:5;i:4955128;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:25:"PUT worker/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258723;i:4;a:0:{}i:5;i:4955936;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:32:"POST worker/profile/upload-audio";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258738;i:4;a:0:{}i:5;i:4956752;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258754;i:4;a:0:{}i:5;i:4957560;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/auth/verify-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258771;i:4;a:0:{}i:5;i:4958368;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:40:"POST employer/auth/complete-registration";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258787;i:4;a:0:{}i:5;i:4959200;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/auth/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.2588;i:4;a:0:{}i:5;i:4960008;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:25:"POST employer/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258817;i:4;a:0:{}i:5;i:4960816;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/worker/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.25883;i:4;a:0:{}i:5;i:4961624;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258843;i:4;a:0:{}i:5;i:4962432;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/detail";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258857;i:4;a:0:{}i:5;i:4964520;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/worker/professions";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.25887;i:4;a:0:{}i:5;i:4965328;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:33:"GET employer/worker/by-profession";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258883;i:4;a:0:{}i:5;i:4966144;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/worker/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258895;i:4;a:0:{}i:5;i:4966952;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/worker/check-access";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258908;i:4;a:0:{}i:5;i:4967768;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/worker/unlock-contact";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.25892;i:4;a:0:{}i:5;i:4968584;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST employer/favorite/add";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258936;i:4;a:0:{}i:5;i:4969392;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/favorite/remove";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258952;i:4;a:0:{}i:5;i:4970200;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/favorite/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258965;i:4;a:0:{}i:5;i:4971008;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/favorite/toggle";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258981;i:4;a:0:{}i:5;i:4971816;}i:43;a:6:{i:0;a:3:{s:4:"rule";s:31:"POST employer/favorite/bulk-add";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.258997;i:4;a:0:{}i:5;i:4972624;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:34:"POST employer/favorite/bulk-remove";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259013;i:4;a:0:{}i:5;i:4973440;}i:45;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/favorite/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259024;i:4;a:0:{}i:5;i:4974256;}i:46;a:6:{i:0;a:3:{s:4:"rule";s:35:"GET employer/favorite/by-profession";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259035;i:4;a:0:{}i:5;i:4975072;}i:47;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/profile/view";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259046;i:4;a:0:{}i:5;i:4975880;}i:48;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT employer/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259058;i:4;a:0:{}i:5;i:4976688;}i:49;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259071;i:4;a:0:{}i:5;i:4977496;}i:50;a:6:{i:0;a:3:{s:4:"rule";s:37:"POST employer/profile/change-language";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259085;i:4;a:0:{}i:5;i:4978312;}i:51;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/profile/languages";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259096;i:4;a:0:{}i:5;i:4979120;}i:52;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/profile/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259109;i:4;a:0:{}i:5;i:4979928;}i:53;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259124;i:4;a:0:{}i:5;i:4980736;}i:54;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/vacancy/view";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259137;i:4;a:0:{}i:5;i:4981544;}i:55;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/create";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259153;i:4;a:0:{}i:5;i:4982352;}i:56;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT employer/vacancy/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259166;i:4;a:0:{}i:5;i:4983160;}i:57;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259182;i:4;a:0:{}i:5;i:4983968;}i:58;a:6:{i:0;a:3:{s:4:"rule";s:30:"DELETE employer/vacancy/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259195;i:4;a:0:{}i:5;i:4984776;}i:59;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259211;i:4;a:0:{}i:5;i:4985584;}i:60;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/vacancy/statuses";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259224;i:4;a:0:{}i:5;i:4986392;}i:61;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/access/filter-page";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259237;i:4;a:0:{}i:5;i:4987200;}i:62;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/access/calculate-price";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259252;i:4;a:0:{}i:5;i:4988016;}i:63;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/access/purchase";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259268;i:4;a:0:{}i:5;i:4988824;}i:64;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/access/my-purchases";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.25928;i:4;a:0:{}i:5;i:4989640;}i:65;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/access/available";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259294;i:4;a:0:{}i:5;i:4996080;}i:66;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/access/unlock-contact";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259307;i:4;a:0:{}i:5;i:4996896;}i:67;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/access/contact-status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.25932;i:4;a:0:{}i:5;i:4997712;}i:68;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/access/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259333;i:4;a:0:{}i:5;i:4998520;}i:69;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/tariff/plans";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259346;i:4;a:0:{}i:5;i:4999328;}i:70;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/tariff/detail";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259359;i:4;a:0:{}i:5;i:5000136;}i:71;a:6:{i:0;a:3:{s:4:"rule";s:27:"GET employer/tariff/compare";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259371;i:4;a:0:{}i:5;i:5000944;}i:72;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/tariff/filtered";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259383;i:4;a:0:{}i:5;i:5001752;}i:73;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/tariff/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259397;i:4;a:0:{}i:5;i:5002560;}i:74;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/tariff/recommended";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.25941;i:4;a:0:{}i:5;i:5003368;}i:75;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/tariff/discounts";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259422;i:4;a:0:{}i:5;i:5004176;}i:76;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/payment/methods";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259435;i:4;a:0:{}i:5;i:5004984;}i:77;a:6:{i:0;a:3:{s:4:"rule";s:27:"GET employer/payment/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259447;i:4;a:0:{}i:5;i:5005792;}i:78;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/payment/history";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259459;i:4;a:0:{}i:5;i:5006600;}i:79;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/payment/callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259474;i:4;a:0:{}i:5;i:5007408;}i:80;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/payment/click-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.25949;i:4;a:0:{}i:5;i:5008224;}i:81;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/payment/payme-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259505;i:4;a:0:{}i:5;i:5009040;}i:82;a:6:{i:0;a:3:{s:4:"rule";s:37:"POST employer/payment/uzcard-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259521;i:4;a:0:{}i:5;i:5009856;}i:83;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/payment/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259534;i:4;a:0:{}i:5;i:5010664;}i:84;a:6:{i:0;a:3:{s:4:"rule";s:27:"POST employer/payment/retry";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259549;i:4;a:0:{}i:5;i:5011472;}i:85;a:6:{i:0;a:3:{s:4:"rule";s:21:"employer/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259563;i:4;a:0:{}i:5;i:5012224;}i:86;a:6:{i:0;a:3:{s:4:"rule";s:29:"employer/vacancy/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259574;i:4;a:0:{}i:5;i:5012976;}i:87;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259585;i:4;a:0:{}i:5;i:5013728;}i:88;a:6:{i:0;a:3:{s:4:"rule";s:12:"<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259598;i:4;a:0:{}i:5;i:5014480;}i:89;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753269898.259606;i:4;a:0:{}i:5;i:5014856;}}s:5:"route";s:23:"employer/auth/send-code";s:6:"action";s:65:"app\modules\employer\controllers\AuthController::actionSendCode()";}";s:7:"request";s:3506:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:500;s:14:"requestHeaders";a:10:{s:12:"content-type";s:16:"application/json";s:10:"user-agent";s:21:"PostmanRuntime/7.44.1";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"b5276279-7334-4d95-b54e-2d5f27b96fde";s:4:"host";s:7:"vacanct";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:2:"32";s:6:"cookie";s:42:"PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k";}s:15:"responseHeaders";a:8:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:32:"Access-Control-Allow-Credentials";s:5:"false";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6880c68a3a1ad";s:16:"X-Debug-Duration";s:3:"346";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6880c68a3a1ad";}s:5:"route";s:23:"employer/auth/send-code";s:6:"action";s:65:"app\modules\employer\controllers\AuthController::actionSendCode()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:32:"{
    "phone": "+998930960195"
}";s:7:"Decoded";a:0:{}}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:12:"CONTENT_TYPE";s:16:"application/json";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.1";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"b5276279-7334-4d95-b54e-2d5f27b96fde";s:9:"HTTP_HOST";s:7:"vacanct";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:2:"32";s:11:"HTTP_COOKIE";s:42:"PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:7:"vacanct";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:40:"D:/OSPanel/domains/ish_top/web/index.php";s:11:"REMOTE_PORT";s:5:"59437";s:12:"REDIRECT_URL";s:24:"/employer/auth/send-code";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:25:"//employer/auth/send-code";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1753269898.119455;s:12:"REQUEST_TIME";i:1753269898;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:1:{s:9:"PHPSESSID";s:32:"gnoa088o0hon1vgqigs0tb9cu9vd163k";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"6880c68a3a1ad";s:3:"url";s:39:"http://vacanct//employer/auth/send-code";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753269898.119455;s:10:"statusCode";i:500;s:8:"sqlCount";i:4;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8030656;s:14:"processingTime";d:0.3506758213043213;}s:10:"exceptions";a:0:{}}