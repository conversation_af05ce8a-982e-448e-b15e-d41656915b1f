<?php

// Отладочный скрипт для проверки точного текста SMS
require_once __DIR__ . '/vendor/autoload.php';

defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

require __DIR__ . '/vendor/yiisoft/yii2/Yii.php';

$config = require __DIR__ . '/config/web.php';
(new yii\web\Application($config));

echo "=== Отладка текста SMS ===\n\n";

// Тестируем для employer
$userType = 'employer';
$code = '1234';
$userTypeText = $userType === 'worker' ? 'Ishchi' : 'Ish beruvchi';
$message = "Tasdiqlash kodi {$userTypeText} hisobiga kirish uchun IshTop platformasida: Kod: {$code}";

echo "Тип пользователя: {$userType}\n";
echo "Текст для типа: {$userTypeText}\n";
echo "Код: {$code}\n\n";

echo "Сформированный текст SMS:\n";
echo "'{$message}'\n\n";

echo "Длина текста: " . strlen($message) . " символов\n\n";

echo "Одобренный шаблон из eskiz.uz:\n";
echo "'Tasdiqlash kodi [user_type] hisobiga kirish uchun IshTop platformasida: Kod: [code]'\n\n";

echo "Ожидаемый текст для Ish beruvchi с кодом 1234:\n";
echo "'Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234'\n\n";

// Сравниваем символ за символом
$expected = "Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234";
echo "Сравнение:\n";
echo "Наш текст:     '{$message}'\n";
echo "Ожидаемый:     '{$expected}'\n";
echo "Совпадают: " . ($message === $expected ? "ДА" : "НЕТ") . "\n\n";

if ($message !== $expected) {
    echo "Различия:\n";
    $len = max(strlen($message), strlen($expected));
    for ($i = 0; $i < $len; $i++) {
        $char1 = isset($message[$i]) ? $message[$i] : '(конец)';
        $char2 = isset($expected[$i]) ? $expected[$i] : '(конец)';
        if ($char1 !== $char2) {
            echo "Позиция {$i}: наш='{$char1}' ожидаемый='{$char2}'\n";
        }
    }
}

echo "\n=== Тест завершен ===\n";
