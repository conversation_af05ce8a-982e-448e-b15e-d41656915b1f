<?php

// Тест SMS с детальным логированием
require_once __DIR__ . '/vendor/autoload.php';

defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

require __DIR__ . '/vendor/yiisoft/yii2/Yii.php';

$config = require __DIR__ . '/config/console.php';
new yii\console\Application($config);

use app\common\services\SmsService;

echo "=== Тест SMS с детальным логированием ===\n";

try {
    $smsService = new SmsService();
    
    $testPhone = '+998930960195';
    $testCode = '1234';
    $userType = 'employer';
    
    echo "Телефон: {$testPhone}\n";
    echo "Код: {$testCode}\n";
    echo "Тип: {$userType}\n\n";
    
    echo "1. Тестирование авторизации...\n";
    $authResult = $smsService->authorize();
    echo "Результат авторизации: " . ($authResult ? "✅ УСПЕШНО" : "❌ ОШИБКА") . "\n\n";
    
    if ($authResult) {
        echo "2. Отправка SMS...\n";
        $smsResult = $smsService->sendValidationCode($testPhone, $testCode, $userType);
        
        echo "Результат отправки SMS:\n";
        echo json_encode($smsResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
        
        if ($smsResult['success']) {
            echo "✅ SMS отправлен успешно!\n";
        } else {
            echo "❌ Ошибка отправки SMS:\n";
            echo "Ошибка: " . ($smsResult['error'] ?? 'Неизвестная ошибка') . "\n";
            if (isset($smsResult['details'])) {
                echo "Детали: {$smsResult['details']}\n";
            }
        }
    }
    
    echo "\n3. Проверка SMS логов...\n";
    $logDir = __DIR__ . '/runtime/logs/sms';
    $logFile = $logDir . '/sms_' . date('Y-m-d') . '.log';
    
    if (file_exists($logFile)) {
        echo "✅ Лог файл найден: {$logFile}\n";
        $logContent = file_get_contents($logFile);
        $logLines = explode("\n", $logContent);
        
        echo "Последние записи в SMS логе:\n";
        echo "----------------------------------------\n";
        foreach (array_slice($logLines, -20) as $line) {
            if (!empty(trim($line))) {
                echo $line . "\n";
            }
        }
        echo "----------------------------------------\n";
    } else {
        echo "❌ Лог файл не найден: {$logFile}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Исключение: " . $e->getMessage() . "\n";
    echo "Файл: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== Тест завершен ===\n";
